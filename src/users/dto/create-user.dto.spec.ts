import { validate } from 'class-validator';
import { CreateUserDto } from './create-user.dto';

describe('CreateUserDto', () => {
  let dto: CreateUserDto;
  beforeEach(() => {
    dto = new CreateUserDto();
    dto.name = '<PERSON>';
    dto.email = '<EMAIL>';
    dto.password = 'password123';
  });

  it('should be defined', () => {
    expect(dto).toBeDefined();
  });

  it('should validate complex data', async () => {
    const errors = await validate(dto);
    expect(errors).toHaveLength(0);
  });

  it('should validate invalid email format', async () => {
    dto.email = 'invalid-email';
    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    console.log(errors);
    expect(errors[0].property).toBe('email');
  });
});
