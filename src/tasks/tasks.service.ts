import { Injectable } from '@nestjs/common';
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Task } from './entities/task.entity';
import { TaskLabel } from './entities/task-label.entity';
import { FindOptionsWhere, Like, Repository } from 'typeorm';
import { WrongTaskStatusException } from './exceptions/wrong-task-status.exception';
import { UserNotFoundException } from './exceptions/user-not-found.exception';
import { TaskStatus } from './task.model';
import { User } from 'src/users/entities/user.entity';
import { CreateTaskLabelDto } from './dto/create-task-label.dto';
import { FindTaskParams } from './find-task.params';
import { PaginationParams } from 'src/common/pagination.params';

@Injectable()
export class TasksService {
  constructor(
    @InjectRepository(Task) private taskRepository: Repository<Task>,
    @InjectRepository(TaskLabel)
    private taskLabelRepository: Repository<TaskLabel>,
    @InjectRepository(User) private userRepository: Repository<User>,
  ) {}

  public async findAll(
    filters: FindTaskParams,
    pagination: PaginationParams,
  ): Promise<[Task[], number]> {
    const query = this.taskRepository
      .createQueryBuilder('task')
      .leftJoinAndSelect('task.labels', 'labels');

    if (filters.status) {
      query.andWhere('task.status = :status', { status: filters.status });
    }

    if (filters.search?.trim()) {
      query.andWhere(
        'task.title ILIKE :search OR task.description ILIKE :search',
        {
          search: `%${filters.search}%`,
        },
      );
    }

    if (
      filters.labels &&
      Array.isArray(filters.labels) &&
      filters.labels.length > 0
    ) {
      const subQuery = query
        .subQuery()
        .select('labels.taskId')
        .from('task_labels', 'labels')
        .where('labels.name IN (:...labels)', {
          labels: filters.labels,
        })
        .getQuery();

      query.andWhere(`task.id IN (${subQuery})`);
    }

    query.orderBy(`task.${filters.sortBy || 'createdAt'}`, filters.sortOrder);

    query.skip(pagination.offset).take(pagination.limit);
    return query.getManyAndCount();
  }

  public async findOne(id: string): Promise<Task | null> {
    return await this.taskRepository.findOne({
      where: { id },
      relations: ['user', 'labels'],
    });
  }

  public async createTask(createTaskDto: CreateTaskDto): Promise<Task> {
    const { userId, ...taskData } = createTaskDto;

    // Find the user by ID
    const user = await this.userRepository.findOneBy({ id: userId });
    if (!user) {
      throw new UserNotFoundException(userId);
    }

    if (createTaskDto.labels) {
      createTaskDto.labels = this.getUniqueLabels(createTaskDto.labels);
    }

    const task = this.taskRepository.create({
      ...taskData,
      user,
    });

    const savedTask = await this.taskRepository.save(task);

    // Create labels if provided
    if (createTaskDto.labels) {
      const taskLabels = createTaskDto.labels.map((labelDto) =>
        this.taskLabelRepository.create({
          name: labelDto.name,
          task: savedTask,
        }),
      );
      await this.taskLabelRepository.save(taskLabels);
    }

    // Return the task with labels
    return (await this.findOne(savedTask.id)) as Task;
  }

  public async updateTask(
    task: Task,
    updateTaskDto: UpdateTaskDto,
  ): Promise<Task> {
    if (
      updateTaskDto.status &&
      !this.isValidStatusTransition(task.status, updateTaskDto.status)
    ) {
      throw new WrongTaskStatusException();
    }

    Object.assign(task, updateTaskDto);
    return await this.taskRepository.save(task);
  }

  public async deleteTask(task: Task): Promise<void> {
    await this.taskRepository.delete(task.id);
  }

  public async addLabels(
    task: Task,
    labelDtos: CreateTaskLabelDto[],
  ): Promise<Task> {
    // 1) Deduplicate DTOs
    // 2) Get existing names
    // 3) New labels aren't already exiting ones
    // 4) We save new ones, only if there are any real new ones
    const existingNames = new Set(task.labels.map((label) => label.name));

    // sanitize the user input to ensure no duplicates
    labelDtos = this.getUniqueLabels(labelDtos);
    const taskLabels = labelDtos
      // filter out existing names
      .filter((labelDto) => !existingNames.has(labelDto.name))
      .map((labelDto) =>
        this.taskLabelRepository.create({
          name: labelDto.name,
          task,
        }),
      );
    await this.taskLabelRepository.save(taskLabels);
    return (await this.findOne(task.id)) as Task;
  }

  public async removeLabels(
    task: Task,
    labelsToRemove: string[],
  ): Promise<Task> {
    // Find labels to delete
    const labelsToDelete = task.labels.filter((label) =>
      labelsToRemove.includes(label.name),
    );

    if (labelsToDelete.length > 0) {
      // Delete the label entities directly
      await this.taskLabelRepository.remove(labelsToDelete);
    }

    return (await this.findOne(task.id)) as Task;
  }

  private getUniqueLabels(
    labelDtos: CreateTaskLabelDto[],
  ): CreateTaskLabelDto[] {
    const uniqueNames = [
      ...new Set(labelDtos.map((labelDto) => labelDto.name)),
    ];
    return uniqueNames.map((name) => ({ name }));
  }

  private isValidStatusTransition(
    currentStatus: TaskStatus,
    newStatus: TaskStatus,
  ): boolean {
    const statusOrder = [
      TaskStatus.OPEN,
      TaskStatus.IN_PROGRESS,
      TaskStatus.DONE,
    ];

    return statusOrder.indexOf(currentStatus) <= statusOrder.indexOf(newStatus);
  }
}
