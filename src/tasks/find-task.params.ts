import { IsEnum, IsIn, <PERSON><PERSON>ptional, IsString, MinLength } from 'class-validator';
import { TaskStatus } from './task.model';
import { Transform } from 'class-transformer';

export class FindTaskParams {
  @IsOptional()
  @IsEnum(TaskStatus)
  status?: TaskStatus;

  @IsOptional()
  @IsString()
  @MinLength(3)
  search?: string;

  @IsOptional()
  @Transform(({ value }: { value?: string | string[] }) => {
    if (!value) return undefined;

    // Handle case where value is already an array (shouldn't happen with query params, but just in case)
    if (Array.isArray(value)) {
      return value.filter((label) => label && label.trim().length > 0);
    }

    // Handle string input
    if (typeof value === 'string') {
      const labels = value
        .split(',')
        .map((label) => label.trim())
        .filter((label) => label.length > 0);

      return labels.length > 0 ? labels : undefined;
    }

    return undefined;
  })
  labels?: string[];

  @IsOptional()
  @IsString()
  @IsIn(['createdAt', 'title', 'status'])
  sortBy?: string;

  @IsOptional()
  @IsIn(['ASC', 'DESC'])
  sortOrder?: 'ASC' | 'DESC' = 'DESC';
}
