version: '3.8'
services:
  postgres:
    image: postgres:16
    container_name: my_postgres
    environment:
      POSTGRES_USER: process.env.POSTGRES_USER
      POSTGRES_PASSWORD: process.env.POSTGRES_PASSWORD
      POSTGRES_DB: process.env.POSTGRES_DB
    ports:
      - 5431:5432
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U postgres -d tasks']
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

volumes:
  postgres_data:
